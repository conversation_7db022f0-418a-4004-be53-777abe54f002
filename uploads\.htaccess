# Allow access to all uploaded files
Options +FollowSymLinks
RewriteEngine On

# Set proper MIME types for images
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ico)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Cache-Control "public, max-age=2592000"
</FilesMatch>

# Set proper MIME types for videos
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    Header set Accept-Ranges bytes
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</FilesMatch>

# Set proper MIME types for documents
<FilesMatch "\.(pdf|doc|docx|txt)$">
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>

# Enable caching for all media files
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ico|mp4|avi|mov|webm|mkv|pdf)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Prevent access to sensitive files
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Allow directory browsing (optional, for debugging)
# Options +Indexes